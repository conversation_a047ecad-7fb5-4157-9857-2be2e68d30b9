// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";
import "../Fixture.t.sol";

/**
 * @title Protocol Fee Realistic Constraints POC
 * @notice Tests the vulnerability under realistic system limitations and permissions
 */
contract ProtocolFeeRealisticConstraintsPOC is Fixture {
    FeeManager public feeManager;
    address public vault;
    address public asset;
    
    uint256 constant ONE_YEAR_SECONDS = 365 days;
    
    function setUp() external {
        Deployment memory deployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        feeManager = deployment.feeManager;
        vault = address(deployment.vault);
        asset = assetsDefault[0];
        
        vm.startPrank(deployment.vaultAdmin);
        feeManager.setBaseAsset(vault, asset);
        vm.stopPrank();
        
        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);
    }
    
    /**
     * @notice Test under realistic fee validation constraints
     */
    function testRealistic_FeeValidationConstraints() external {
        // Realistic constraint: Total fees cannot exceed 100%
        // This is enforced by the _setFees function
        
        // Test maximum realistic protocol fee (when other fees are also set)
        uint24 depositFee = 1e3;     // 0.1%
        uint24 redeemFee = 1e3;      // 0.1%
        uint24 performanceFee = 2e4; // 2%
        uint24 maxProtocolFee = 1e6 - depositFee - redeemFee - performanceFee; // ~97.8%
        
        vm.prank(vaultAdmin);
        feeManager.setFees(depositFee, redeemFee, performanceFee, maxProtocolFee);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        
        // Even with ~98% protocol fee, the bug makes it only ~0.098%
        uint256 expectedBuggyFee = (1000 ether * maxProtocolFee) / (1e6 * 1000);
        
        assertEq(actualFee, expectedBuggyFee, "Bug affects even maximum allowed protocol fee");
        assertTrue(actualFee < 1 ether, "Even 98% protocol fee results in <0.1% actual fee");
    }
    
    /**
     * @notice Test under realistic permission constraints
     */
    function testRealistic_PermissionConstraints() external {
        // Only owner can set fees
        address nonOwner = address(0x123);
        
        vm.prank(nonOwner);
        vm.expectRevert();
        feeManager.setFees(0, 0, 0, 1e4);
        
        // But anyone can observe the bug through calculateFee
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        // Different actors can all observe the same buggy calculation
        vm.prank(nonOwner);
        uint256 feeFromNonOwner = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        
        vm.prank(vaultAdmin);
        uint256 feeFromOwner = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        
        uint256 expectedBuggyFee = 1000 ether / 100000; // 1% / 1000
        
        assertEq(feeFromNonOwner, expectedBuggyFee, "Non-owner observes bug");
        assertEq(feeFromOwner, expectedBuggyFee, "Owner observes same bug");
        assertEq(feeFromNonOwner, feeFromOwner, "Bug is observable by anyone");
    }
    
    /**
     * @notice Test under realistic gas constraints
     */
    function testRealistic_GasConstraints() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        // Test gas usage is reasonable
        uint256 gasStart = gasleft();
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        uint256 gasUsed = gasStart - gasleft();
        
        // Should use reasonable gas (less than a simple transfer)
        assertTrue(gasUsed < 50000, "Gas usage should be reasonable");
        assertTrue(fee > 0, "Should calculate fee despite gas constraints");
        
        // Bug doesn't make the function more expensive
        assertEq(fee, 1000 ether / 100000, "Bug result with reasonable gas");
    }
    
    /**
     * @notice Test under realistic time constraints
     */
    function testRealistic_TimeConstraints() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4);
        
        // Test realistic time periods that might occur in production
        uint256[] memory realisticPeriods = new uint256[](6);
        realisticPeriods[0] = 1 hours;   // Frequent updates
        realisticPeriods[1] = 1 days;    // Daily updates
        realisticPeriods[2] = 1 weeks;   // Weekly updates
        realisticPeriods[3] = 30 days;   // Monthly updates
        realisticPeriods[4] = 90 days;   // Quarterly updates
        realisticPeriods[5] = 365 days;  // Annual updates
        
        for (uint256 i = 0; i < realisticPeriods.length; i++) {
            // Reset timestamp
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
            
            vm.warp(block.timestamp + realisticPeriods[i]);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
            uint256 expectedBuggyFee = (1000 ether * 1e4 * realisticPeriods[i]) / (1e6 * 1000 * ONE_YEAR_SECONDS);
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug affects realistic period ", vm.toString(i))));
        }
    }
    
    /**
     * @notice Test under realistic vault size constraints
     */
    function testRealistic_VaultSizeConstraints() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4); // 1%
        
        // Realistic vault sizes in DeFi
        uint256[] memory realisticSizes = new uint256[](7);
        realisticSizes[0] = 1_000 ether;        // $1k vault (small)
        realisticSizes[1] = 10_000 ether;       // $10k vault
        realisticSizes[2] = 100_000 ether;      // $100k vault
        realisticSizes[3] = 1_000_000 ether;    // $1M vault
        realisticSizes[4] = 10_000_000 ether;   // $10M vault
        realisticSizes[5] = 100_000_000 ether;  // $100M vault (large)
        realisticSizes[6] = 1_000_000_000 ether; // $1B vault (very large)
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        for (uint256 i = 0; i < realisticSizes.length; i++) {
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, realisticSizes[i]);
            uint256 expectedBuggyFee = realisticSizes[i] / 100000; // 1% / 1000
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug affects vault size ", vm.toString(i))));
            
            // Calculate lost revenue for each size
            uint256 intendedFee = realisticSizes[i] / 100; // 1%
            uint256 lostRevenue = intendedFee - actualFee;
            
            // Even small vaults lose significant revenue
            if (realisticSizes[i] >= 100_000 ether) {
                assertTrue(lostRevenue > 990 ether, "Significant revenue loss even for medium vaults");
            }
        }
    }
    
    /**
     * @notice Test under realistic fee rate constraints
     */
    function testRealistic_FeeRateConstraints() external {
        // Realistic protocol fee rates used in DeFi
        uint24[] memory realisticRates = new uint24[](6);
        realisticRates[0] = 1e3;  // 0.1% (very low)
        realisticRates[1] = 5e3;  // 0.5% (low)
        realisticRates[2] = 1e4;  // 1% (moderate)
        realisticRates[3] = 2e4;  // 2% (high)
        realisticRates[4] = 5e4;  // 5% (very high)
        realisticRates[5] = 1e5;  // 10% (extreme)
        
        uint256 vaultSize = 1_000_000 ether; // $1M vault
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        for (uint256 i = 0; i < realisticRates.length; i++) {
            vm.prank(vaultAdmin);
            feeManager.setFees(0, 0, 0, realisticRates[i]);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, vaultSize);
            uint256 expectedBuggyFee = (vaultSize * realisticRates[i]) / (1e6 * 1000);
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug affects rate ", vm.toString(i))));
            
            // Even extreme rates are reduced to negligible amounts
            if (realisticRates[i] == 1e5) { // 10%
                uint256 intendedFee = vaultSize / 10; // 10% of $1M = $100k
                assertEq(actualFee, 100 ether, "10% becomes 0.01% due to bug");
                assertEq(intendedFee, 100_000 ether, "Intended fee is $100k");
            }
        }
    }
    
    /**
     * @notice Test under realistic network conditions
     */
    function testRealistic_NetworkConditions() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4);
        
        // Simulate realistic network conditions
        // Test that the bug persists regardless of block properties
        
        uint256 originalTimestamp = block.timestamp;
        uint256 originalBlockNumber = block.number;
        
        // Simulate different block times
        vm.warp(originalTimestamp + ONE_YEAR_SECONDS);
        vm.roll(originalBlockNumber + 2_628_000); // ~1 year of blocks at 12s intervals
        
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        uint256 expectedBuggyFee = 1000 ether / 100000;
        
        assertEq(fee, expectedBuggyFee, "Bug persists under realistic network conditions");
        
        // Test with different block.timestamp values
        vm.warp(type(uint32).max); // Far future timestamp
        uint256 feeInFuture = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        
        // The bug calculation should still work (though with a very large time difference)
        assertTrue(feeInFuture > expectedBuggyFee, "Bug calculation works even with extreme timestamps");
    }
    
    /**
     * @notice Test integration with existing unit tests
     */
    function testRealistic_ExistingTestCompatibility() external {
        // Replicate the exact scenario from existing unit tests
        vm.prank(vaultAdmin);
        feeManager.setFees(1e5, 2e5, 3e5, 4e5); // From FeeManager.t.sol
        
        vm.warp(block.timestamp + 365 days);
        
        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, 1 ether);
        
        // The existing test expects 4 * 1 ether / 10 = 0.4 ether
        // This expectation is wrong - it should be 400 ether (40% of 1 ether)
        uint256 existingTestExpectation = 4 * 1 ether / 10;
        uint256 correctExpectation = 400 ether;
        
        assertEq(actualFee, existingTestExpectation, "Matches existing (buggy) test");
        assertNotEq(actualFee, correctExpectation, "Does not match correct expectation");
        
        // This proves the existing tests validate the bug, not the intended behavior
        assertTrue(correctExpectation == actualFee * 1000, "Correct fee is 1000x larger");
    }
    
    /**
     * @notice Test real-world economic impact
     */
    function testRealistic_EconomicImpact() external {
        // Simulate a realistic DeFi protocol scenario
        uint256 protocolTVL = 500_000_000 ether; // $500M total value locked
        uint24 competitiveProtocolFee = 5e3; // 0.5% annual fee (competitive rate)
        
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, competitiveProtocolFee);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 actualAnnualRevenue = feeManager.calculateFee(vault, asset, 1 ether, protocolTVL);
        uint256 intendedAnnualRevenue = (protocolTVL * competitiveProtocolFee) / 1e6;
        uint256 lostAnnualRevenue = intendedAnnualRevenue - actualAnnualRevenue;
        
        // Expected: $2.5M annual revenue (0.5% of $500M)
        // Actual: $2.5k annual revenue (due to bug)
        // Lost: $2.4975M annual revenue
        
        assertEq(intendedAnnualRevenue, 2_500_000 ether, "Should generate $2.5M annually");
        assertEq(actualAnnualRevenue, 2_500 ether, "Actually generates $2.5k annually");
        assertEq(lostAnnualRevenue, 2_497_500 ether, "Loses $2.4975M annually");
        
        // Over 5 years, the cumulative loss would be ~$12.5M
        uint256 fiveYearLoss = lostAnnualRevenue * 5;
        assertEq(fiveYearLoss, 12_487_500 ether, "5-year loss: $12.5M");
        
        // This demonstrates the critical nature of the vulnerability
        assertTrue(lostAnnualRevenue > actualAnnualRevenue * 999, "Lost revenue is >999x actual revenue");
    }
}
