// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";
import "../Fixture.t.sol";

/**
 * @title Protocol Fee Persistence POC
 * @notice Verifies the vulnerability persists across different conditions and states
 */
contract ProtocolFeePersistencePOC is Fixture {
    FeeManager public feeManager;
    address public vault;
    address public asset;
    
    uint256 constant ONE_YEAR_SECONDS = 365 days;
    uint24 constant PROTOCOL_FEE_1_PERCENT = 1e4;
    
    function setUp() external {
        Deployment memory deployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        feeManager = deployment.feeManager;
        vault = address(deployment.vault);
        asset = assetsDefault[0];
        
        vm.startPrank(deployment.vaultAdmin);
        feeManager.setBaseAsset(vault, asset);
        vm.stopPrank();
        
        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);
    }
    
    /**
     * @notice Test persistence across multiple fee updates
     */
    function testPersistence_MultipleFeeUpdates() external {
        uint256 totalShares = 1000 ether;
        
        // Test different protocol fee values
        uint24[] memory protocolFees = new uint24[](4);
        protocolFees[0] = 5e3;  // 0.5%
        protocolFees[1] = 1e4;  // 1%
        protocolFees[2] = 5e4;  // 5%
        protocolFees[3] = 1e5;  // 10%
        
        for (uint256 i = 0; i < protocolFees.length; i++) {
            // Reset timestamp
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
            
            // Set new protocol fee
            vm.prank(vaultAdmin);
            feeManager.setFees(0, 0, 0, protocolFees[i]);
            
            // Wait 1 year
            vm.warp(block.timestamp + ONE_YEAR_SECONDS);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
            uint256 expectedBuggyFee = (totalShares * protocolFees[i]) / (1e6 * 1000);
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug persists for fee ", vm.toString(i))));
            
            // Verify it's 1000x smaller than intended
            uint256 intendedFee = (totalShares * protocolFees[i]) / 1e6;
            assertEq(actualFee, intendedFee / 1000, string(abi.encodePacked("1000x smaller for fee ", vm.toString(i))));
        }
    }
    
    /**
     * @notice Test persistence across different vault states
     */
    function testPersistence_DifferentVaultStates() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        // Test with different total share amounts
        uint256[] memory shareAmounts = new uint256[](5);
        shareAmounts[0] = 1 ether;
        shareAmounts[1] = 100 ether;
        shareAmounts[2] = 10_000 ether;
        shareAmounts[3] = 1_000_000 ether;
        shareAmounts[4] = 100_000_000 ether;
        
        for (uint256 i = 0; i < shareAmounts.length; i++) {
            // Reset timestamp
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
            
            vm.warp(block.timestamp + ONE_YEAR_SECONDS);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, shareAmounts[i]);
            uint256 expectedBuggyFee = shareAmounts[i] / 100000; // 1% / 1000 = 0.001%
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug persists for shares ", vm.toString(i))));
        }
    }
    
    /**
     * @notice Test persistence across different time periods
     */
    function testPersistence_DifferentTimePeriods() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        uint256 totalShares = 1000 ether;
        
        // Test different time periods
        uint256[] memory timePeriods = new uint256[](6);
        timePeriods[0] = 1 days;
        timePeriods[1] = 1 weeks;
        timePeriods[2] = 30 days;
        timePeriods[3] = 90 days;
        timePeriods[4] = 180 days;
        timePeriods[5] = 365 days;
        
        for (uint256 i = 0; i < timePeriods.length; i++) {
            // Reset timestamp
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
            
            vm.warp(block.timestamp + timePeriods[i]);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
            
            // Calculate expected buggy fee
            uint256 expectedBuggyFee = (totalShares * PROTOCOL_FEE_1_PERCENT * timePeriods[i]) / (1e6 * 1000 * ONE_YEAR_SECONDS);
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug persists for period ", vm.toString(i))));
            
            // Verify proportional scaling is also affected by the bug
            if (i > 0) {
                uint256 previousPeriodFee = (totalShares * PROTOCOL_FEE_1_PERCENT * timePeriods[i-1]) / (1e6 * 1000 * ONE_YEAR_SECONDS);
                assertTrue(actualFee > previousPeriodFee, "Longer periods have higher fees (but still buggy)");
            }
        }
    }
    
    /**
     * @notice Test persistence across multiple vaults
     */
    function testPersistence_MultipleVaults() external {
        // Create additional vaults
        Deployment memory vault2Deployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        Deployment memory vault3Deployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        
        address vault2 = address(vault2Deployment.vault);
        address vault3 = address(vault3Deployment.vault);
        
        // Set up all vaults with the same fee manager
        FeeManager sharedFeeManager = vault2Deployment.feeManager;
        
        vm.startPrank(vault2Deployment.vaultAdmin);
        sharedFeeManager.setBaseAsset(vault2, asset);
        sharedFeeManager.setBaseAsset(vault3, asset);
        sharedFeeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        vm.stopPrank();
        
        // Initialize timestamps for all vaults
        vm.prank(vault2);
        sharedFeeManager.updateState(asset, 1 ether);
        vm.prank(vault3);
        sharedFeeManager.updateState(asset, 1 ether);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 totalShares = 1000 ether;
        
        // Test that bug affects all vaults equally
        uint256 fee2 = sharedFeeManager.calculateFee(vault2, asset, 1 ether, totalShares);
        uint256 fee3 = sharedFeeManager.calculateFee(vault3, asset, 1 ether, totalShares);
        
        uint256 expectedBuggyFee = totalShares / 100000; // 1% / 1000
        
        assertEq(fee2, expectedBuggyFee, "Bug affects vault 2");
        assertEq(fee3, expectedBuggyFee, "Bug affects vault 3");
        assertEq(fee2, fee3, "Bug affects all vaults equally");
    }
    
    /**
     * @notice Test persistence across contract upgrades (if applicable)
     */
    function testPersistence_ContractState() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        uint256 totalShares = 1000 ether;
        
        // Test that the bug persists regardless of when the calculation is called
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        // Multiple calls should return the same buggy result
        uint256 fee1 = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        uint256 fee2 = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        uint256 fee3 = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        
        assertEq(fee1, fee2, "Multiple calls return same buggy result");
        assertEq(fee2, fee3, "Bug is deterministic");
        
        uint256 expectedBuggyFee = totalShares / 100000;
        assertEq(fee1, expectedBuggyFee, "All calls return buggy amount");
    }
    
    /**
     * @notice Test persistence across different price conditions
     */
    function testPersistence_DifferentPrices() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        uint256 totalShares = 1000 ether;
        
        // Test different price values (protocol fee shouldn't depend on price)
        uint256[] memory prices = new uint256[](5);
        prices[0] = 0.5 ether;  // 50% of initial
        prices[1] = 1 ether;    // Same as initial
        prices[2] = 2 ether;    // 200% of initial
        prices[3] = 10 ether;   // 1000% of initial
        prices[4] = 0.1 ether;  // 10% of initial
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 expectedBuggyFee = totalShares / 100000;
        
        for (uint256 i = 0; i < prices.length; i++) {
            uint256 fee = feeManager.calculateFee(vault, asset, prices[i], totalShares);
            assertEq(fee, expectedBuggyFee, string(abi.encodePacked("Bug persists for price ", vm.toString(i))));
        }
    }
    
    /**
     * @notice Test persistence across different block timestamps
     */
    function testPersistence_DifferentBlockTimes() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        uint256 totalShares = 1000 ether;
        
        // Test at different absolute timestamps
        uint256[] memory timestamps = new uint256[](4);
        timestamps[0] = block.timestamp + ONE_YEAR_SECONDS;
        timestamps[1] = block.timestamp + 2 * ONE_YEAR_SECONDS;
        timestamps[2] = block.timestamp + 10 * ONE_YEAR_SECONDS;
        timestamps[3] = block.timestamp + 100 * ONE_YEAR_SECONDS;
        
        for (uint256 i = 0; i < timestamps.length; i++) {
            // Reset timestamp
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
            
            vm.warp(timestamps[i]);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
            
            // Calculate expected fee for the time period
            uint256 timePassed = timestamps[i] - feeManager.timestamps(vault);
            uint256 expectedBuggyFee = (totalShares * PROTOCOL_FEE_1_PERCENT * timePassed) / (1e6 * 1000 * ONE_YEAR_SECONDS);
            
            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug persists at timestamp ", vm.toString(i))));
        }
    }
    
    /**
     * @notice Test that the bug is inherent to the code, not environmental
     */
    function testPersistence_CodeInherent() external {
        // The bug is in the hardcoded constant 365e6 days
        // This test verifies it's not dependent on external factors
        
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        // Test with different msg.sender values
        address[] memory senders = new address[](3);
        senders[0] = address(this);
        senders[1] = vaultAdmin;
        senders[2] = address(0x999);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 expectedBuggyFee = 1000 ether / 100000; // 1000 ether * 1% / 1000
        
        for (uint256 i = 0; i < senders.length; i++) {
            vm.prank(senders[i]);
            uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
            assertEq(fee, expectedBuggyFee, string(abi.encodePacked("Bug independent of sender ", vm.toString(i))));
        }
        
        // The bug is deterministic and always produces the same wrong result
        // This confirms it's a code-level issue, not environmental
    }
}
