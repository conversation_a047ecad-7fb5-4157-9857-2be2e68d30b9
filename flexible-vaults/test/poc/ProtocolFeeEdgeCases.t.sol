// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";
import "../Fixture.t.sol";

/**
 * @title Protocol Fee Edge Cases POC
 * @notice Tests edge cases and boundary conditions for the protocol fee vulnerability
 */
contract ProtocolFeeEdgeCasesPOC is Fixture {
    FeeManager public feeManager;
    address public vault;
    address public asset;
    
    uint256 constant ONE_YEAR_SECONDS = 365 days;
    
    function setUp() external {
        Deployment memory deployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        feeManager = deployment.feeManager;
        vault = address(deployment.vault);
        asset = assetsDefault[0];
        
        vm.startPrank(deployment.vaultAdmin);
        feeManager.setBaseAsset(vault, asset);
        vm.stopPrank();
        
        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);
    }
    
    /**
     * @notice Test edge case: Maximum possible values
     */
    function testEdgeCase_MaximumValues() external {
        // Maximum protocol fee
        uint24 maxProtocolFee = 1e6; // 100%
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, maxProtocolFee);
        
        // Maximum total shares (realistic maximum)
        uint256 maxTotalShares = type(uint128).max;
        
        // Maximum time period (100 years)
        uint256 maxTimePeriod = 100 * ONE_YEAR_SECONDS;
        
        vm.warp(block.timestamp + maxTimePeriod);
        
        // This should not overflow due to the bug making the result much smaller
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, maxTotalShares);
        
        // Expected without bug: would likely overflow
        // Actual with bug: 100 years * 100% / 1000 = 10% of maxTotalShares
        uint256 expectedBuggyFee = maxTotalShares / 10;
        
        assertEq(fee, expectedBuggyFee, "Even maximum values affected by 1000x bug");
        assertTrue(fee < maxTotalShares, "Fee should be less than total shares");
    }
    
    /**
     * @notice Test edge case: Minimum non-zero values
     */
    function testEdgeCase_MinimumValues() external {
        // Minimum protocol fee (1 in D6 precision = 0.0001%)
        uint24 minProtocolFee = 1;
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, minProtocolFee);
        
        // Minimum total shares
        uint256 minTotalShares = 1;
        
        // Minimum time period (1 second)
        vm.warp(block.timestamp + 1);
        
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, minTotalShares);
        
        // With the bug, even minimum values might round to 0
        // Expected: (1 * 1 * 1) / (365e6 * 86400) = 0 (rounds down)
        assertEq(fee, 0, "Minimum values round to zero due to bug");
    }
    
    /**
     * @notice Test edge case: Zero values
     */
    function testEdgeCase_ZeroValues() external {
        // Zero protocol fee
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 0);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        assertEq(fee, 0, "Zero protocol fee results in zero fee");
        
        // Zero total shares
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4);
        
        uint256 feeZeroShares = feeManager.calculateFee(vault, asset, 1 ether, 0);
        assertEq(feeZeroShares, 0, "Zero total shares results in zero fee");
    }
    
    /**
     * @notice Test edge case: Time boundary conditions
     */
    function testEdgeCase_TimeBoundaries() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4); // 1%
        
        uint256 totalShares = 1000 ether;
        
        // Test exactly at timestamp (no time passed)
        uint256 feeNoTime = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        assertEq(feeNoTime, 0, "No fee when no time has passed");
        
        // Test 1 second after
        vm.warp(block.timestamp + 1);
        uint256 fee1Second = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        assertTrue(fee1Second >= 0, "Fee calculation works for 1 second");
        
        // Test exactly 1 year
        vm.prank(vault);
        feeManager.updateState(asset, 1 ether); // Reset timestamp
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 fee1Year = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        uint256 expectedBuggy1Year = (totalShares * 1e4) / (1e6 * 1000); // 1% / 1000
        assertEq(fee1Year, expectedBuggy1Year, "1 year fee matches buggy calculation");
    }
    
    /**
     * @notice Test edge case: Precision and rounding
     */
    function testEdgeCase_PrecisionRounding() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1); // Minimum fee: 0.0001%
        
        // Test various share amounts to see rounding behavior
        uint256[] memory shareAmounts = new uint256[](5);
        shareAmounts[0] = 1;
        shareAmounts[1] = 1000;
        shareAmounts[2] = 1e6;
        shareAmounts[3] = 1e12;
        shareAmounts[4] = 1e18;
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        for (uint256 i = 0; i < shareAmounts.length; i++) {
            uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, shareAmounts[i]);
            
            // Due to the bug, most small amounts will round to 0
            // Only very large amounts will have non-zero fees
            if (shareAmounts[i] < 1e15) {
                assertEq(fee, 0, string(abi.encodePacked("Small amount rounds to 0: ", vm.toString(i))));
            } else {
                assertTrue(fee >= 0, string(abi.encodePacked("Large amount has fee: ", vm.toString(i))));
            }
        }
    }
    
    /**
     * @notice Test edge case: Multiple timestamp updates
     */
    function testEdgeCase_MultipleTimestampUpdates() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4); // 1%
        
        uint256 totalShares = 1000 ether;
        
        // First period: 30 days
        vm.warp(block.timestamp + 30 days);
        uint256 fee30Days = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        
        // Update timestamp (simulating fee collection)
        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);
        
        // Second period: another 30 days
        vm.warp(block.timestamp + 30 days);
        uint256 feeNext30Days = feeManager.calculateFee(vault, asset, 1 ether, totalShares);
        
        // Both periods should have the same fee (30 days each)
        assertEq(fee30Days, feeNext30Days, "Equal time periods should have equal fees");
        
        // Verify the bug affects both calculations equally
        uint256 expected30DayFee = (totalShares * 1e4 * 30 days) / (1e6 * 1000 * ONE_YEAR_SECONDS);
        assertEq(fee30Days, expected30DayFee, "First period matches buggy calculation");
        assertEq(feeNext30Days, expected30DayFee, "Second period matches buggy calculation");
    }
    
    /**
     * @notice Test edge case: Overflow prevention due to bug
     */
    function testEdgeCase_OverflowPrevention() external {
        // Set maximum protocol fee
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e6); // 100%
        
        // Use large values that might cause overflow without the bug
        uint256 largeShares = type(uint128).max;
        uint256 longTime = 1000 * ONE_YEAR_SECONDS; // 1000 years
        
        vm.warp(block.timestamp + longTime);
        
        // Without the bug, this calculation would be:
        // (largeShares * 1e6 * longTime) / (365 * 86400 * 1e6)
        // = (largeShares * longTime) / (365 * 86400)
        // This could overflow for very large values
        
        // With the bug, the calculation is:
        // (largeShares * 1e6 * longTime) / (365e6 * 86400)
        // = (largeShares * longTime) / (365 * 1e6 * 86400)
        // This is 1000x smaller and less likely to overflow
        
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, largeShares);
        
        // The bug actually prevents overflow in extreme cases
        assertTrue(fee > 0, "Fee calculation succeeds even with extreme values");
        assertTrue(fee < largeShares, "Fee is reasonable due to bug reducing it");
        
        // Expected fee with bug: 1000 years * 100% / 1000 = 100% of shares
        assertEq(fee, largeShares, "Extreme case results in 100% fee due to bug");
    }
    
    /**
     * @notice Test edge case: Gas consumption
     */
    function testEdgeCase_GasConsumption() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, 1e4);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        // Measure gas consumption
        uint256 gasBefore = gasleft();
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, 1000 ether);
        uint256 gasUsed = gasBefore - gasleft();
        
        // Gas consumption should be reasonable
        assertTrue(gasUsed < 50000, "Gas consumption should be reasonable");
        assertTrue(fee > 0, "Fee calculation should work");
        
        // The bug doesn't affect gas consumption significantly
        // Both correct and buggy calculations have similar complexity
    }
}
