// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";

/**
 * @title RedirectingDepositHook Vulnerability POC
 * @notice Comprehensive proof-of-concept demonstrating the asset allocation vulnerability
 * @dev This POC demonstrates how assets can remain unallocated after the distribution loop completes
 */
contract RedirectingDepositHookVulnerabilityPOC is Test {
    MockERC20 asset;
    MockVault vault;
    address subvault1;
    address subvault2;
    address subvault3;
    
    // Test accounts
    address alice = vm.createWallet("alice").addr;
    address bob = vm.createWallet("bob").addr;
    
    function setUp() external {
        asset = new MockERC20();
        vault = new MockVault();
        subvault1 = vm.createWallet("subvault1").addr;
        subvault2 = vm.createWallet("subvault2").addr;
        subvault3 = vm.createWallet("subvault3").addr;
    }

    /**
     * @notice POC Test 1: Demonstrates the core vulnerability
     * @dev Shows how assets remain unallocated when total subvault capacity < deposit amount
     */
    function testVulnerability_CoreIssue_UnallocatedAssets() external {
        console.log("=== POC Test 1: Core Vulnerability - Unallocated Assets ===");
        
        // STEP 1: Setup vault with limited subvault capacity
        console.log("Step 1: Setting up vault with limited capacity");
        
        // Add subvaults with specific capacity limits
        vault.addSubvault(subvault1, asset, 0 ether);
        vault.addSubvault(subvault2, asset, 0 ether);
        vault.addSubvault(subvault3, asset, 0 ether);
        
        // Create risk manager and set individual subvault limits
        vault.addRiskManager(0); // Default limit of 0
        MockRiskManager riskManager = MockRiskManager(address(vault.riskManager()));
        
        // Set capacity limits: Total capacity = 700 USDC
        riskManager.setSubvaultLimit(subvault1, 300 ether); // 300 USDC capacity
        riskManager.setSubvaultLimit(subvault2, 400 ether); // 400 USDC capacity  
        riskManager.setSubvaultLimit(subvault3, 0 ether);   // 0 USDC capacity (inactive)
        
        console.log("Subvault 1 capacity:", riskManager.maxDeposit(subvault1, address(asset)));
        console.log("Subvault 2 capacity:", riskManager.maxDeposit(subvault2, address(asset)));
        console.log("Subvault 3 capacity:", riskManager.maxDeposit(subvault3, address(asset)));
        console.log("Total subvault capacity: 700 USDC");
        
        // STEP 2: Simulate user deposit larger than total capacity
        uint256 userDeposit = 1000 ether; // 1000 USDC deposit
        console.log("Step 2: User deposits", userDeposit / 1 ether, "USDC");
        
        // Mint assets to vault (simulating deposit queue transfer)
        asset.mint(address(vault), userDeposit);
        uint256 vaultBalanceBefore = asset.balanceOf(address(vault));
        console.log("Vault balance before hook:", vaultBalanceBefore / 1 ether, "USDC");
        
        // STEP 3: Execute the vulnerable hook
        console.log("Step 3: Executing RedirectingDepositHook");
        vault.afterDepositHookCall(address(asset), userDeposit);
        
        // STEP 4: Measure the impact
        console.log("Step 4: Measuring impact");
        
        uint256 subvault1Balance = asset.balanceOf(subvault1);
        uint256 subvault2Balance = asset.balanceOf(subvault2);
        uint256 subvault3Balance = asset.balanceOf(subvault3);
        uint256 vaultBalanceAfter = asset.balanceOf(address(vault));
        
        console.log("Subvault 1 received:", subvault1Balance / 1 ether, "USDC");
        console.log("Subvault 2 received:", subvault2Balance / 1 ether, "USDC");
        console.log("Subvault 3 received:", subvault3Balance / 1 ether, "USDC");
        console.log("Vault balance after hook:", vaultBalanceAfter / 1 ether, "USDC");
        
        uint256 totalAllocated = subvault1Balance + subvault2Balance + subvault3Balance;
        uint256 unallocatedAssets = vaultBalanceAfter;
        
        console.log("Total allocated to subvaults:", totalAllocated / 1 ether, "USDC");
        console.log("Unallocated assets remaining in vault:", unallocatedAssets / 1 ether, "USDC");
        
        // STEP 5: Verify the vulnerability
        console.log("Step 5: Vulnerability verification");
        
        // Assert that assets were distributed according to capacity
        assertEq(subvault1Balance, 300 ether, "Subvault 1 should receive 300 USDC");
        assertEq(subvault2Balance, 400 ether, "Subvault 2 should receive 400 USDC");
        assertEq(subvault3Balance, 0 ether, "Subvault 3 should receive 0 USDC");
        
        // Critical assertion: Assets remain unallocated
        assertEq(unallocatedAssets, 300 ether, "300 USDC should remain unallocated in vault");
        assertEq(totalAllocated, 700 ether, "Only 700 USDC should be allocated to subvaults");
        
        // The vulnerability: User would receive shares for 1000 USDC but only 700 USDC is deployed
        console.log("VULNERABILITY CONFIRMED:");
        console.log("- User deposit: 1000 USDC");
        console.log("- Assets deployed: 700 USDC");
        console.log("- Assets unallocated: 300 USDC");
        console.log("- User would receive shares for full 1000 USDC despite incomplete allocation");
    }
}