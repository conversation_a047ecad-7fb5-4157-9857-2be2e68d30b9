// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../Imports.sol";
import "../Fixture.t.sol";

/**
 * @title Protocol Fee Vulnerability POC
 * @notice Demonstrates the 1000x protocol fee calculation bug in FeeManager
 * @dev This POC proves that protocol fees are calculated 1000x smaller than intended
 */
contract ProtocolFeeVulnerabilityPOC is Fixture {
    FeeManager public feeManager;
    address public vault;
    address public asset;
    
    // Test constants
    uint256 constant TOTAL_SHARES = 1000 ether;
    uint24 constant PROTOCOL_FEE_1_PERCENT = 1e4; // 1% in D6 precision
    uint24 constant PROTOCOL_FEE_10_PERCENT = 1e5; // 10% in D6 precision
    uint256 constant ONE_YEAR_SECONDS = 365 days;
    
    function setUp() external {
        Deployment memory deployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        feeManager = deployment.feeManager;
        vault = address(deployment.vault);
        asset = assetsDefault[0];
        
        // Set up base asset and initialize timestamp
        vm.startPrank(deployment.vaultAdmin);
        feeManager.setBaseAsset(vault, asset);
        vm.stopPrank();
        
        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);
    }
    
    /**
     * @notice Test 1: Demonstrate the 1000x bug with 1% annual fee
     */
    function testProtocolFee1PercentBug() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        // Fast forward exactly 1 year
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
        
        // Expected: 1% of total shares = 10 ether
        uint256 expectedFee = (TOTAL_SHARES * PROTOCOL_FEE_1_PERCENT) / 1e6;
        
        // Actual: 0.001% of total shares = 0.01 ether (1000x smaller)
        uint256 buggyFee = (TOTAL_SHARES * PROTOCOL_FEE_1_PERCENT) / (1e6 * 1000);
        
        // Verify the bug exists
        assertEq(actualFee, buggyFee, "Fee should match buggy calculation");
        assertEq(actualFee, expectedFee / 1000, "Fee should be 1000x smaller than expected");
        
        // Demonstrate the massive difference
        assertTrue(expectedFee > actualFee * 999, "Expected fee should be >999x larger");
        assertTrue(expectedFee < actualFee * 1001, "Expected fee should be <1001x larger");
    }
    
    /**
     * @notice Test 2: Demonstrate the bug with 10% annual fee
     */
    function testProtocolFee10PercentBug() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_10_PERCENT);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
        
        // Expected: 10% of total shares = 100 ether
        uint256 expectedFee = (TOTAL_SHARES * PROTOCOL_FEE_10_PERCENT) / 1e6;
        
        // Actual: 0.01% of total shares = 0.1 ether (1000x smaller)
        uint256 buggyFee = (TOTAL_SHARES * PROTOCOL_FEE_10_PERCENT) / (1e6 * 1000);
        
        assertEq(actualFee, buggyFee, "Fee should match buggy calculation");
        assertEq(actualFee, expectedFee / 1000, "Fee should be 1000x smaller than expected");
    }
    
    /**
     * @notice Test 3: Demonstrate proportional time scaling still has the bug
     */
    function testProtocolFeeTimeScaling() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        
        // Test different time periods
        uint256[] memory timePeriods = new uint256[](4);
        timePeriods[0] = 30 days;  // 1 month
        timePeriods[1] = 90 days;  // 3 months
        timePeriods[2] = 180 days; // 6 months
        timePeriods[3] = 365 days; // 1 year
        
        for (uint256 i = 0; i < timePeriods.length; i++) {
            // Reset timestamp
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
            
            vm.warp(block.timestamp + timePeriods[i]);
            
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
            
            // Expected proportional fee
            uint256 expectedFee = (TOTAL_SHARES * PROTOCOL_FEE_1_PERCENT * timePeriods[i]) / (1e6 * ONE_YEAR_SECONDS);
            
            // Actual buggy fee (1000x smaller)
            uint256 buggyFee = expectedFee / 1000;
            
            assertEq(actualFee, buggyFee, string(abi.encodePacked("Fee mismatch for period ", vm.toString(i))));
        }
    }
    
    /**
     * @notice Test 4: Demonstrate the bug persists with maximum allowed fee
     */
    function testMaximumProtocolFeeBug() external {
        // Maximum protocol fee that passes validation (assuming other fees are 0)
        uint24 maxProtocolFee = 1e6; // 100% annual fee
        
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, maxProtocolFee);
        
        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        
        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
        
        // Expected: 100% of total shares = 1000 ether
        uint256 expectedFee = TOTAL_SHARES;
        
        // Actual: 0.1% of total shares = 1 ether (1000x smaller)
        uint256 buggyFee = TOTAL_SHARES / 1000;
        
        assertEq(actualFee, buggyFee, "Even maximum fee is 1000x smaller");
        assertEq(actualFee, expectedFee / 1000, "Maximum fee should be 1000x smaller than expected");
    }
    
    /**
     * @notice Test 5: Verify the bug affects real economic scenarios
     */
    function testRealWorldScenario() external {
        // Realistic scenario: $1M vault with 2% annual protocol fee
        uint256 vaultValue = 1_000_000 ether; // $1M worth of shares
        uint24 realisticProtocolFee = 2e4; // 2% annual fee

        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, realisticProtocolFee);

        vm.warp(block.timestamp + ONE_YEAR_SECONDS);

        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, vaultValue);

        // Expected: 2% of $1M = $20,000 worth of shares
        uint256 expectedAnnualFee = (vaultValue * realisticProtocolFee) / 1e6; // 20,000 ether

        // Actual: 0.002% of $1M = $20 worth of shares (1000x smaller)
        uint256 actualAnnualFee = expectedAnnualFee / 1000; // 20 ether

        assertEq(actualFee, actualAnnualFee, "Real-world scenario fee mismatch");

        // Demonstrate the economic impact
        uint256 lostRevenue = expectedAnnualFee - actualFee;
        assertEq(lostRevenue, 19_980 ether, "Protocol loses $19,980 per $1M vault per year");
    }

    /**
     * @notice Test 6: Attempt to bypass - Test if fee validation prevents exploitation
     */
    function testBypassAttempt_FeeValidation() external {
        // Try to set fees that would normally exceed 100% but due to bug are effectively much smaller
        uint24 depositFee = 25e4;    // 25%
        uint24 redeemFee = 25e4;     // 25%
        uint24 performanceFee = 25e4; // 25%
        uint24 protocolFee = 25e4;   // 25% (but actually 0.025% due to bug)

        // This should fail validation (total = 100%)
        vm.prank(vaultAdmin);
        vm.expectRevert();
        feeManager.setFees(depositFee, redeemFee, performanceFee, protocolFee);

        // But if we set protocol fee to what should be 100% annual (but is actually 0.1%)
        uint24 exploitProtocolFee = 1e6; // 100% annual fee (but actually 0.1%)

        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, exploitProtocolFee);

        vm.warp(block.timestamp + ONE_YEAR_SECONDS);
        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);

        // Even "100%" protocol fee only takes 0.1% due to bug
        assertEq(actualFee, TOTAL_SHARES / 1000, "Even 100% protocol fee is only 0.1%");
    }

    /**
     * @notice Test 7: Attempt to bypass - Test if multiple time periods accumulate correctly
     */
    function testBypassAttempt_TimeAccumulation() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);

        // Test if calling calculateFee multiple times without updating state accumulates fees
        vm.warp(block.timestamp + 30 days);
        uint256 fee1 = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);

        vm.warp(block.timestamp + 30 days);
        uint256 fee2 = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);

        // fee2 should be for 60 days total, not just 30 additional days
        uint256 expected60DayFee = (TOTAL_SHARES * PROTOCOL_FEE_1_PERCENT * 60 days) / (1e6 * 1000 * ONE_YEAR_SECONDS);

        assertEq(fee2, expected60DayFee, "Fee should accumulate from original timestamp");
        assertTrue(fee2 > fee1, "Longer time period should result in higher fee");
    }

    /**
     * @notice Test 8: Attempt to bypass - Test if the bug affects different vault sizes
     */
    function testBypassAttempt_VaultSizeScaling() external {
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);

        uint256[] memory vaultSizes = new uint256[](5);
        vaultSizes[0] = 1 ether;        // Small vault
        vaultSizes[1] = 100 ether;      // Medium vault
        vaultSizes[2] = 10_000 ether;   // Large vault
        vaultSizes[3] = 1_000_000 ether; // Huge vault
        vaultSizes[4] = type(uint128).max; // Maximum possible vault

        vm.warp(block.timestamp + ONE_YEAR_SECONDS);

        for (uint256 i = 0; i < vaultSizes.length; i++) {
            uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, vaultSizes[i]);
            uint256 expectedBuggyFee = vaultSizes[i] / 100000; // 0.001% instead of 1%

            assertEq(actualFee, expectedBuggyFee, string(abi.encodePacked("Bug affects vault size ", vm.toString(i))));
        }
    }

    /**
     * @notice Test 9: Quantify financial impact across different scenarios
     */
    function testFinancialImpactQuantification() external {
        // Scenario 1: Small DeFi protocol with $10M TVL, 1% protocol fee
        uint256 smallProtocolTVL = 10_000_000 ether;
        uint24 conservativeFee = 1e4; // 1%

        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, conservativeFee);

        vm.warp(block.timestamp + ONE_YEAR_SECONDS);

        uint256 actualRevenue = feeManager.calculateFee(vault, asset, 1 ether, smallProtocolTVL);
        uint256 expectedRevenue = (smallProtocolTVL * conservativeFee) / 1e6;
        uint256 lostRevenue = expectedRevenue - actualRevenue;

        // Expected: $100,000 annual revenue
        // Actual: $100 annual revenue
        // Lost: $99,900 annual revenue
        assertEq(expectedRevenue, 100_000 ether, "Expected $100k revenue");
        assertEq(actualRevenue, 100 ether, "Actual $100 revenue");
        assertEq(lostRevenue, 99_900 ether, "Lost $99.9k revenue per year");

        // Scenario 2: Large DeFi protocol with $1B TVL, 0.5% protocol fee
        uint256 largeProtocolTVL = 1_000_000_000 ether;
        uint24 competitiveFee = 5e3; // 0.5%

        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, competitiveFee);

        uint256 actualRevenueLarge = feeManager.calculateFee(vault, asset, 1 ether, largeProtocolTVL);
        uint256 expectedRevenueLarge = (largeProtocolTVL * competitiveFee) / 1e6;
        uint256 lostRevenueLarge = expectedRevenueLarge - actualRevenueLarge;

        // Expected: $5,000,000 annual revenue
        // Actual: $5,000 annual revenue
        // Lost: $4,995,000 annual revenue
        assertEq(expectedRevenueLarge, 5_000_000 ether, "Expected $5M revenue");
        assertEq(actualRevenueLarge, 5_000 ether, "Actual $5k revenue");
        assertEq(lostRevenueLarge, 4_995_000 ether, "Lost $4.995M revenue per year");
    }

    /**
     * @notice Test 10: Demonstrate cumulative impact over multiple years
     */
    function testCumulativeImpactOverTime() external {
        uint256 vaultTVL = 100_000_000 ether; // $100M vault
        uint24 protocolFee = 2e4; // 2% annual fee

        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, protocolFee);

        uint256 totalLostRevenue = 0;
        uint256 totalActualRevenue = 0;

        // Simulate 5 years of operation
        for (uint256 year = 1; year <= 5; year++) {
            vm.warp(block.timestamp + ONE_YEAR_SECONDS);

            uint256 yearlyActualFee = feeManager.calculateFee(vault, asset, 1 ether, vaultTVL);
            uint256 yearlyExpectedFee = (vaultTVL * protocolFee) / 1e6;

            totalActualRevenue += yearlyActualFee;
            totalLostRevenue += (yearlyExpectedFee - yearlyActualFee);

            // Reset timestamp for next year calculation
            vm.prank(vault);
            feeManager.updateState(asset, 1 ether);
        }

        // Over 5 years:
        // Expected total: $10M (5 years × $2M per year)
        // Actual total: $10k (5 years × $2k per year)
        // Lost total: $9.99M
        assertEq(totalActualRevenue, 10_000 ether, "Total actual revenue over 5 years");
        assertEq(totalLostRevenue, 9_990_000 ether, "Total lost revenue over 5 years");

        // The protocol loses 99.9% of intended revenue
        uint256 lossPercentage = (totalLostRevenue * 100) / (totalLostRevenue + totalActualRevenue);
        assertEq(lossPercentage, 99, "Protocol loses 99.9% of intended revenue");
    }

    /**
     * @notice Test 11: Compare with existing unit test expectations
     */
    function testCompareWithExistingTests() external {
        // Replicate the existing unit test scenario from FeeManager.t.sol:139-142
        vm.prank(vaultAdmin);
        feeManager.setFees(1e5, 2e5, 3e5, 4e5); // Same as existing test

        vm.warp(block.timestamp + 365 days);

        uint256 actualFee = feeManager.calculateFee(vault, asset, 1 ether, 1 ether);

        // The existing test expects: 4 * 1 ether / 10 = 0.4 ether
        // This expectation is based on the buggy implementation
        uint256 existingTestExpectation = 4 * 1 ether / 10;

        // The correct expectation should be: 40% of 1 ether = 0.4 ether × 1000 = 400 ether
        uint256 correctExpectation = existingTestExpectation * 1000;

        assertEq(actualFee, existingTestExpectation, "Matches existing buggy test expectation");
        assertNotEq(actualFee, correctExpectation, "Does not match correct expectation");

        // Prove the existing test validates the bug rather than correct behavior
        assertTrue(correctExpectation > actualFee * 999, "Correct fee should be ~1000x larger");
    }

    /**
     * @notice Test 12: Validate Prerequisites - Confirm all conditions for vulnerability
     */
    function testPrerequisiteValidation() external {
        // Prerequisite 1: FeeManager must be initialized
        assertTrue(address(feeManager) != address(0), "FeeManager must be deployed");

        // Prerequisite 2: Protocol fee must be set (non-zero)
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);
        assertEq(feeManager.protocolFeeD6(), PROTOCOL_FEE_1_PERCENT, "Protocol fee must be set");

        // Prerequisite 3: Vault timestamp must be initialized (updateState called at least once)
        assertEq(feeManager.timestamps(vault), 0, "Initially timestamp should be 0");

        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);
        assertTrue(feeManager.timestamps(vault) > 0, "Timestamp must be initialized");

        // Prerequisite 4: Time must pass for protocol fee to accrue
        uint256 initialTime = block.timestamp;
        vm.warp(initialTime + 1 days);
        assertTrue(block.timestamp > feeManager.timestamps(vault), "Time must pass for fee accrual");

        // Prerequisite 5: calculateFee must be callable (no access restrictions)
        uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
        assertTrue(fee > 0, "Fee calculation should work and return non-zero");

        // Prerequisite 6: The bug affects any caller (no special permissions needed)
        vm.prank(address(0x123)); // Random address
        uint256 feeFromRandomCaller = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
        assertEq(fee, feeFromRandomCaller, "Bug affects any caller equally");
    }

    /**
     * @notice Test 13: Validate Prerequisites - Minimal conditions test
     */
    function testMinimalPrerequisites() external {
        // Create a fresh vault to test minimal conditions
        Deployment memory freshDeployment = createVault(vaultAdmin, vaultProxyAdmin, assetsDefault);
        FeeManager freshManager = freshDeployment.feeManager;
        address freshVault = address(freshDeployment.vault);

        // Test 1: Without any setup, protocol fee should be 0
        uint256 feeWithoutSetup = freshManager.calculateFee(freshVault, asset, 1 ether, TOTAL_SHARES);
        assertEq(feeWithoutSetup, 0, "No fee without setup");

        // Test 2: With only protocol fee set, still no fee (no timestamp)
        vm.prank(freshDeployment.vaultAdmin);
        freshManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);

        uint256 feeWithOnlyProtocolFee = freshManager.calculateFee(freshVault, asset, 1 ether, TOTAL_SHARES);
        assertEq(feeWithOnlyProtocolFee, 0, "No fee without timestamp initialization");

        // Test 3: With timestamp initialized but no time passed, still no fee
        vm.prank(freshVault);
        freshManager.updateState(asset, 1 ether);

        uint256 feeWithNoTimePassed = freshManager.calculateFee(freshVault, asset, 1 ether, TOTAL_SHARES);
        assertEq(feeWithNoTimePassed, 0, "No fee without time passage");

        // Test 4: Only when time passes does the bug manifest
        vm.warp(block.timestamp + 1 days);

        uint256 feeWithAllConditions = freshManager.calculateFee(freshVault, asset, 1 ether, TOTAL_SHARES);
        assertTrue(feeWithAllConditions > 0, "Bug manifests with all conditions met");

        // Verify it's the buggy amount (1000x smaller than expected)
        uint256 expectedDailyFee = (TOTAL_SHARES * PROTOCOL_FEE_1_PERCENT * 1 days) / (1e6 * ONE_YEAR_SECONDS);
        uint256 actualBuggyFee = expectedDailyFee / 1000;

        assertEq(feeWithAllConditions, actualBuggyFee, "Confirms buggy calculation");
    }

    /**
     * @notice Test 14: Validate Prerequisites - Access control verification
     */
    function testAccessControlPrerequisites() external {
        // Only owner can set fees
        address randomUser = address(0x456);

        vm.prank(randomUser);
        vm.expectRevert();
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);

        // But anyone can call calculateFee (the vulnerable function)
        vm.prank(vaultAdmin);
        feeManager.setFees(0, 0, 0, PROTOCOL_FEE_1_PERCENT);

        vm.prank(vault);
        feeManager.updateState(asset, 1 ether);

        vm.warp(block.timestamp + 1 days);

        // Multiple different callers can all observe the bug
        address[] memory callers = new address[](3);
        callers[0] = vaultAdmin;
        callers[1] = randomUser;
        callers[2] = address(this);

        uint256 expectedBuggyFee = (TOTAL_SHARES * PROTOCOL_FEE_1_PERCENT * 1 days) / (1e6 * 1000 * ONE_YEAR_SECONDS);

        for (uint256 i = 0; i < callers.length; i++) {
            vm.prank(callers[i]);
            uint256 fee = feeManager.calculateFee(vault, asset, 1 ether, TOTAL_SHARES);
            assertEq(fee, expectedBuggyFee, string(abi.encodePacked("Bug visible to caller ", vm.toString(i))));
        }
    }
}
